name: Publish to Comfy registry
on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - "pyproject.toml"

permissions:
  issues: write

jobs:
  publish-node:
    name: Publish Custom Node to registry
    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'ssitu' }}
    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: Include submodule files
        run: |
          # Remove the submodule's .git folder so it becomes a normal directory
          git rm --cached repositories/ultimate_sd_upscale
          rm -rf repositories/ultimate_sd_upscale/.git
          # Add the now "regular" folder to the index
          git add repositories/ultimate_sd_upscale

      - name: Publish Custom Node
        uses: Comfy-Org/publish-node-action@v1
        with:
          ## Add your own personal access token to your Github Repository secrets and reference it here.
          personal_access_token: ${{ secrets.REGISTRY_ACCESS_TOKEN }}
