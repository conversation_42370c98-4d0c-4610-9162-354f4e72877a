# Dynamic Inputs

Some node in comfy mtb use the concept of `dynamic inputs` meaning that their input length can grow. This is usually useful for all sort of cases.
For now there is not builtin way to do that in Comfy but once there is the nodes will just use that.


## List of nodes using dynamic inputs
- Apply Text Template
- Save Data Bundle
- Add to Playlist
- Psd Save
- Stack Images
- Concat Images
- Batch Float Assemble
- Batch Float Math
- Plot Batch Float
- Batch Merge
- Math Expression


![dyninputs](https://github.com/melMass/comfy_mtb/assets/7041726/6ef0ced5-cd6d-46a9-8451-964388183aef)
