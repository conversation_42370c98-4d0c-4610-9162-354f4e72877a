{"t":{"$date":"2025-07-23T09:21:28.918Z"},"s":"E","c":"MONGOSH","id":1000000006,"ctx":"telemetry","msg":"Error: Failed to resolve machine ID","attr":{"stack":"Error: Failed to resolve machine ID\n    at i (eval at module.exports (node:lib-boxednode/mongosh:103:20), <anonymous>:336:192867)\n    at async LoggingAndTelemetry.setupTelemetry (eval at module.exports (node:lib-boxednode/mongosh:103:20), <anonymous>:336:298874)","name":"Error","message":"Failed to resolve machine ID","code":null}}
{"t":{"$date":"2025-07-23T09:21:28.929Z"},"s":"I","c":"MONGOSH","id":1000000005,"ctx":"config","msg":"User updated"}
{"t":{"$date":"2025-07-23T09:21:28.931Z"},"s":"I","c":"MONGOSH","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-07-23T09:21:28.931Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":null},"version":"2.5.2","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-06-02T13:18:06.466Z","gitVersion":"642243f7488ba3bb57ce4b5814da64b4ff9ca2b5","nodeVersion":"v20.19.2","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.39","deps":{"nodeDriverVersion":"6.16.0","libmongocryptVersion":"1.13.0","libmongocryptNodeBindingsVersion":"6.3.0","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-07-23T09:21:28.994Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":295,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-07-23T09:21:29.005Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://127.0.0.1:27017/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh****.2","driver":{"name":"nodejs|mongosh","version":"6.16.0|2.5.2"},"devtoolsConnectVersion":"3.4.1","host":"127.0.0.1:27017"}}
{"t":{"$date":"2025-07-23T09:21:29.009Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"127.0.0.1:27017"}}
{"t":{"$date":"2025-07-23T09:21:29.050Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-07-23T09:21:29.052Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-07-23T09:21:29.054Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
{"t":{"$date":"2025-07-23T09:21:29.057Z"},"s":"I","c":"MONGOSH","id":1000000003,"ctx":"repl","msg":"Start loading CLI scripts"}
{"t":{"$date":"2025-07-23T09:21:29.059Z"},"s":"I","c":"MONGOSH","id":1000000013,"ctx":"repl","msg":"Evaluating script passed on the command line"}
{"t":{"$date":"2025-07-23T09:21:29.060Z"},"s":"I","c":"MONGOSH","id":1000000007,"ctx":"repl","msg":"Evaluating input","attr":{"input":"db.adminCommand('ping')"}}
{"t":{"$date":"2025-07-23T09:21:29.060Z"},"s":"I","c":"MONGOSH","id":1000000011,"ctx":"shell-api","msg":"Performed API call","attr":{"method":"adminCommand","class":"Database","db":"test","arguments":{"cmd":{"ping":1}}}}
{"t":{"$date":"2025-07-23T09:21:29.063Z"},"s":"I","c":"MONGOSH","id":1000000011,"ctx":"shell-api","msg":"Performed API call","attr":{"method":"getSiblingDB","class":"Database","db":"test","arguments":{"db":"admin"}}}
{"t":{"$date":"2025-07-23T09:21:29.068Z"},"s":"I","c":"MONGOSH","id":1000000004,"ctx":"connect","msg":"Connecting to server","attr":{"userId":null,"telemetryAnonymousId":"68793dcaf5a3a0942569e326","connectionUri":"mongodb://<ip address>:27017/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh****.2","is_localhost":true,"is_do_url":false,"is_atlas_url":false,"is_atlas":false,"server_version":"8.0.10","node_version":"v20.19.2","server_os":"linux","server_arch":"x86_64","is_enterprise":false,"auth_type":null,"is_data_federation":false,"is_stream":false,"dl_version":null,"atlas_version":null,"is_genuine":true,"non_genuine_server_name":"mongodb","is_local_atlas":false,"fcv":"8.0","api_version":null,"api_strict":null,"api_deprecation_errors":null,"atlas_hostname":null}}
